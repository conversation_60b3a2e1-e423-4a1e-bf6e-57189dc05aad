using Microsoft.AspNetCore.Identity;
using HotelBooking.Models;
using HotelBooking.Data;
using Microsoft.EntityFrameworkCore;

namespace HotelBooking.Services
{
    public class DataSeeder
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ApplicationDbContext _context;

        public DataSeeder(UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
        }

        public async Task SeedAsync()
        {
            // Seed roles
            await SeedRolesAsync();

            // Seed admin user
            await SeedAdminUserAsync();

            // Seed sample data
            await SeedSampleDataAsync();
        }

        private async Task SeedRolesAsync()
        {
            string[] roles = { "Admin", "Customer" };

            foreach (var role in roles)
            {
                if (!await _roleManager.RoleExistsAsync(role))
                {
                    await _roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private async Task SeedAdminUserAsync()
        {
            var adminEmail = "<EMAIL>";
            var adminUser = await _userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "Quản trị viên",
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                var result = await _userManager.CreateAsync(adminUser, "Admin@123");
                
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }

        private async Task SeedSampleDataAsync()
        {
            // Seed sample rooms if not exist
            if (!await _context.Rooms.AnyAsync())
            {
                var roomTypes = await _context.RoomTypes.ToListAsync();
                var rooms = new List<Room>();

                // Standard rooms
                for (int i = 101; i <= 110; i++)
                {
                    rooms.Add(new Room
                    {
                        RoomNumber = i.ToString(),
                        RoomTypeId = roomTypes.First(rt => rt.Name == "Phòng Standard").Id,
                        Floor = 1,
                        Status = RoomStatus.Available,
                        CreatedAt = DateTime.Now,
                        LastUpdated = DateTime.Now
                    });
                }

                // Deluxe rooms
                for (int i = 201; i <= 205; i++)
                {
                    rooms.Add(new Room
                    {
                        RoomNumber = i.ToString(),
                        RoomTypeId = roomTypes.First(rt => rt.Name == "Phòng Deluxe").Id,
                        Floor = 2,
                        Status = RoomStatus.Available,
                        CreatedAt = DateTime.Now,
                        LastUpdated = DateTime.Now
                    });
                }

                // Suite rooms
                for (int i = 301; i <= 303; i++)
                {
                    rooms.Add(new Room
                    {
                        RoomNumber = i.ToString(),
                        RoomTypeId = roomTypes.First(rt => rt.Name == "Phòng Suite").Id,
                        Floor = 3,
                        Status = RoomStatus.Available,
                        CreatedAt = DateTime.Now,
                        LastUpdated = DateTime.Now
                    });
                }

                _context.Rooms.AddRange(rooms);
                await _context.SaveChangesAsync();
            }

            // Seed sample customer users
            await SeedSampleUsersAsync();

            // Seed sample bookings
            await SeedSampleBookingsAsync();
        }

        private async Task SeedSampleUsersAsync()
        {
            var sampleUsers = new[]
            {
                new { Email = "<EMAIL>", FullName = "Nguyễn Văn An", Phone = "0901234567" },
                new { Email = "<EMAIL>", FullName = "Trần Thị Bình", Phone = "0902345678" },
                new { Email = "<EMAIL>", FullName = "Lê Văn Cường", Phone = "0903456789" }
            };

            foreach (var userData in sampleUsers)
            {
                var existingUser = await _userManager.FindByEmailAsync(userData.Email);
                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        UserName = userData.Email,
                        Email = userData.Email,
                        FullName = userData.FullName,
                        PhoneNumber = userData.Phone,
                        EmailConfirmed = true,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    var result = await _userManager.CreateAsync(user, "Customer@123");
                    if (result.Succeeded)
                    {
                        await _userManager.AddToRoleAsync(user, "Customer");
                    }
                }
            }
        }

        private async Task SeedSampleBookingsAsync()
        {
            if (!await _context.Bookings.AnyAsync())
            {
                var users = await _userManager.GetUsersInRoleAsync("Customer");
                var rooms = await _context.Rooms.Take(5).ToListAsync();

                if (users.Any() && rooms.Any())
                {
                    var bookings = new List<Booking>();
                    var random = new Random();

                    for (int i = 0; i < 10; i++)
                    {
                        var user = users[random.Next(users.Count)];
                        var room = rooms[random.Next(rooms.Count)];
                        var checkInDate = DateTime.Now.AddDays(random.Next(-30, 30));
                        var checkOutDate = checkInDate.AddDays(random.Next(1, 7));
                        var nights = (checkOutDate - checkInDate).Days;

                        bookings.Add(new Booking
                        {
                            BookingCode = $"BK{DateTime.Now.Year}{(i + 1):D4}",
                            UserId = user.Id,
                            RoomId = room.Id,
                            CheckInDate = checkInDate,
                            CheckOutDate = checkOutDate,
                            Adults = random.Next(1, 4),
                            Children = random.Next(0, 2),
                            TotalAmount = room.RoomType.PricePerNight * nights,
                            Status = (BookingStatus)random.Next(0, 6),
                            BookingDate = DateTime.Now.AddDays(-random.Next(1, 60)),
                            SpecialRequests = i % 3 == 0 ? "Phòng tầng cao, view đẹp" : null
                        });
                    }

                    _context.Bookings.AddRange(bookings);
                    await _context.SaveChangesAsync();
                }
            }
        }
    }
}
