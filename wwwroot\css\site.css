html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom styles for hotel booking website */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.navbar-brand {
  font-size: 1.5rem;
}

.hero-section img {
  border-radius: 15px;
}

.services-section .bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

footer {
  background: #2d3748 !important;
}

.search-section .card {
  border: none;
  border-radius: 15px;
}

.rooms-section .card-img-top {
  border-radius: 15px 15px 0 0;
}

/* Image placeholders */
.image-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-image {
  height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.hero-image::before {
  content: '🏨';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 6rem;
  opacity: 0.3;
}

.hero-image::after {
  content: 'Khách sạn sang trọng';
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
}

.room-image {
  height: 250px;
  position: relative;
  overflow: hidden;
}

.room-image.standard {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.room-image.deluxe {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.room-image.suite {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.room-image::before {
  content: '🛏️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 4rem;
  opacity: 0.4;
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.service-icon:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Card hover effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Room image hover effects */
.room-image {
  transition: all 0.3s ease;
  cursor: pointer;
}

.room-image:hover {
  transform: scale(1.05);
}

/* Hero image animation */
.hero-image {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Button hover effects */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Admin Dashboard Images */
.admin-card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin: 0 auto;
}

.admin-card-icon.rooms {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.admin-card-icon.bookings {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.admin-card-icon.users {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.admin-card-icon.revenue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Login/Register Page Images */
.auth-image {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.auth-image::before {
  content: '🏨';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 8rem;
  opacity: 0.2;
}

.auth-image::after {
  content: 'Chào mừng đến với khách sạn của chúng tôi';
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
  color: white;
  font-size: 1.5rem;
  font-weight: 500;
  text-align: center;
}

/* Gallery Images */
.gallery-image {
  height: 300px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-image:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.gallery-image.hotel-exterior {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gallery-image.hotel-lobby {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gallery-image.hotel-restaurant {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gallery-image.hotel-pool {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gallery-image::before {
  content: attr(data-icon);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 4rem;
  opacity: 0.3;
}

.gallery-image::after {
  content: attr(data-title);
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }

  .hero-section .col-lg-6:first-child {
    margin-bottom: 2rem;
  }

  .display-4 {
    font-size: 2rem;
  }

  .hero-image {
    height: 250px;
  }

  .hero-image::before {
    font-size: 4rem;
  }
}