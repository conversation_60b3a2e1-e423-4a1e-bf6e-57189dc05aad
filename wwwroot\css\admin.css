/* Admin Panel Styles */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

#wrapper {
    display: flex;
}

#content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
}

#content {
    flex: 1 0 auto;
}

.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Sidebar */
.sidebar {
    width: 6.5rem;
    min-height: 100vh;
}

.sidebar .nav-item {
    position: relative;
}

.sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

.sidebar .nav-item .nav-link {
    text-align: center;
    padding: 0.75rem 1rem;
    width: 6.5rem;
}

.sidebar .nav-item .nav-link span {
    font-size: 0.65rem;
    display: block;
}

.sidebar.toggled {
    width: 14rem !important;
}

.sidebar.toggled .nav-item .nav-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 14rem;
}

.sidebar.toggled .nav-item .nav-link span {
    font-size: 0.85rem;
    display: inline;
    margin-left: 0.5rem;
}

.sidebar .sidebar-brand {
    height: 4.375rem;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    padding: 1.5rem 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    z-index: 1;
}

.sidebar .sidebar-brand .sidebar-brand-icon i {
    font-size: 2rem;
}

.sidebar .sidebar-brand .sidebar-brand-text {
    display: none;
}

.sidebar.toggled .sidebar-brand .sidebar-brand-text {
    display: block;
}

.sidebar hr.sidebar-divider {
    margin: 0 1rem 1rem;
}

.sidebar .sidebar-heading {
    text-align: center;
    padding: 0 1rem;
    font-weight: 800;
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
}

.sidebar.toggled .sidebar-heading {
    text-align: left;
}

/* Topbar */
.topbar {
    height: 4.375rem;
}

.topbar .navbar-search {
    width: 25rem;
}

.topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

.card .card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Border left utility classes */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* Text utilities */
.text-xs {
    font-size: 0.7rem;
}

.text-lg {
    font-size: 1.2rem;
}

.text-gray-100 {
    color: #f8f9fc !important;
}

.text-gray-200 {
    color: #eaecf4 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-400 {
    color: #d1d3e2 !important;
}

.text-gray-500 {
    color: #b7b9cc !important;
}

.text-gray-600 {
    color: #858796 !important;
}

.text-gray-700 {
    color: #6e707e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-900 {
    color: #3a3b45 !important;
}

/* Background gradient utilities */
.bg-gradient-primary {
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    background-size: cover;
}

.bg-gradient-secondary {
    background-color: #858796;
    background-image: linear-gradient(180deg, #858796 10%, #60616f 100%);
    background-size: cover;
}

/* Buttons */
.btn-primary {
    color: #fff;
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    color: #fff;
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Tables */
.table {
    color: #858796;
}

.table th {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

/* Badges */
.badge-primary {
    color: #fff;
    background-color: #4e73df;
}

.badge-secondary {
    color: #fff;
    background-color: #858796;
}

.badge-success {
    color: #fff;
    background-color: #1cc88a;
}

.badge-info {
    color: #fff;
    background-color: #36b9cc;
}

.badge-warning {
    color: #212529;
    background-color: #f6c23e;
}

.badge-danger {
    color: #fff;
    background-color: #e74a3b;
}

/* Footer */
.sticky-footer {
    flex-shrink: 0;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
}

.scroll-to-top:focus,
.scroll-to-top:hover {
    color: white;
}

.scroll-to-top:hover {
    background: #5a5c69;
}

.scroll-to-top i {
    font-weight: 800;
}

/* Responsive */
@media (min-width: 768px) {
    .sidebar {
        width: 14rem !important;
    }

    .sidebar .nav-item .nav-link {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 14rem;
    }

    .sidebar .nav-item .nav-link span {
        font-size: 0.85rem;
        display: inline;
        margin-left: 0.5rem;
    }

    .sidebar .sidebar-brand .sidebar-brand-text {
        display: block;
    }

    .sidebar .sidebar-heading {
        text-align: left;
    }
}
