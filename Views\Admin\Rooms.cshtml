@model IEnumerable<HotelBooking.Models.Room>
@{
    ViewData["Title"] = "Quản lý phòng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-door-open"></i> Quản lý phòng
            </h1>
            <a asp-action="CreateRoom" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Thêm phòng mới
            </a>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách phòng</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered datatable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Số phòng</th>
                        <th>Loại phòng</th>
                        <th>Tầng</th>
                        <th>Trạng thái</th>
                        <th>Giá/đêm</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var room in Model)
                    {
                        <tr>
                            <td>
                                <strong>@room.RoomNumber</strong>
                            </td>
                            <td>@room.RoomType.Name</td>
                            <td>Tầng @room.Floor</td>
                            <td>
                                <span class="badge badge-@GetStatusBadgeClass(room.Status)">
                                    @room.Status.GetDisplayName()
                                </span>
                            </td>
                            <td>@room.RoomType.PricePerNight.ToString("N0")₫</td>
                            <td>@room.CreatedAt.ToString("dd/MM/yyyy")</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="EditRoom" asp-route-id="@room.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i> Sửa
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger btn-delete" 
                                            onclick="deleteRoom(@room.Id)">
                                        <i class="bi bi-trash"></i> Xóa
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(RoomStatus status)
    {
        return status switch
        {
            RoomStatus.Available => "success",
            RoomStatus.Occupied => "primary",
            RoomStatus.Maintenance => "warning",
            RoomStatus.Cleaning => "info",
            RoomStatus.OutOfOrder => "danger",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <script>
        function deleteRoom(roomId) {
            if (confirm('Bạn có chắc chắn muốn xóa phòng này?')) {
                // Implement delete functionality
                fetch(`/Admin/DeleteRoom/${roomId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra khi xóa phòng');
                    }
                });
            }
        }
    </script>
}
