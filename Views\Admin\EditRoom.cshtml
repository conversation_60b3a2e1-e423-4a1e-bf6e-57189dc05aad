@model HotelBooking.Models.Room
@{
    ViewData["Title"] = "Chỉnh sửa phòng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-pencil"></i> Chỉnh sửa phòng @Model.RoomNumber
            </h1>
            <a asp-action="Rooms" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Thông tin phòng</h6>
            </div>
            <div class="card-body">
                <form asp-action="EditRoom" method="post">
                    <input asp-for="Id" type="hidden" />
                    <input asp-for="CreatedAt" type="hidden" />
                    
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="RoomNumber" class="form-label"></label>
                            <input asp-for="RoomNumber" class="form-control" />
                            <span asp-validation-for="RoomNumber" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="RoomTypeId" class="form-label">Loại phòng</label>
                            <select asp-for="RoomTypeId" class="form-select">
                                @foreach (var roomType in ViewBag.RoomTypes as List<RoomType>)
                                {
                                    <option value="@roomType.Id" selected="@(roomType.Id == Model.RoomTypeId)">
                                        @roomType.Name - @roomType.PricePerNight.ToString("N0")₫/đêm
                                    </option>
                                }
                            </select>
                            <span asp-validation-for="RoomTypeId" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Floor" class="form-label"></label>
                            <input asp-for="Floor" class="form-control" type="number" min="1" max="50" />
                            <span asp-validation-for="Floor" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="Status" class="form-label"></label>
                            <select asp-for="Status" class="form-select">
                                <option value="0" selected="@(Model.Status == RoomStatus.Available)">Có sẵn</option>
                                <option value="1" selected="@(Model.Status == RoomStatus.Occupied)">Đã đặt</option>
                                <option value="2" selected="@(Model.Status == RoomStatus.Maintenance)">Bảo trì</option>
                                <option value="3" selected="@(Model.Status == RoomStatus.Cleaning)">Dọn dẹp</option>
                                <option value="4" selected="@(Model.Status == RoomStatus.OutOfOrder)">Không hoạt động</option>
                            </select>
                            <span asp-validation-for="Status" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label"></label>
                        <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Ngày tạo</label>
                            <input type="text" class="form-control" value="@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Cập nhật lần cuối</label>
                            <input type="text" class="form-control" value="@Model.LastUpdated.ToString("dd/MM/yyyy HH:mm")" readonly />
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Rooms" class="btn btn-secondary me-md-2">Hủy</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
