@model HotelBooking.ViewModels.AdminDashboardViewModel
@{
    ViewData["Title"] = "Bảng điều khiển Admin";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-speedometer2"></i> Bảng điều khiển
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Tổng số phòng</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalRooms</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-door-open fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Tổng đặt phòng</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalBookings</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Người dùng</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalUsers</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Doanh thu</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalRevenue.ToString("N0")₫</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Thống kê đặt phòng theo trạng thái</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="bookingStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Trạng thái đặt phòng</h6>
            </div>
            <div class="card-body">
                @foreach (var status in Model.BookingsByStatus)
                {
                    <h4 class="small font-weight-bold">
                        @status.Key.GetDisplayName()
                        <span class="float-right">@status.Value</span>
                    </h4>
                    <div class="progress mb-4">
                        <div class="progress-bar bg-info" role="progressbar" style="width: @((double)status.Value / Model.TotalBookings * 100)%"></div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Đặt phòng gần đây</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mã đặt phòng</th>
                        <th>Khách hàng</th>
                        <th>Phòng</th>
                        <th>Ngày đặt</th>
                        <th>Trạng thái</th>
                        <th>Tổng tiền</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var booking in Model.RecentBookings)
                    {
                        <tr>
                            <td>@booking.BookingCode</td>
                            <td>@booking.User.FullName</td>
                            <td>@booking.Room.RoomNumber - @booking.Room.RoomType.Name</td>
                            <td>@booking.BookingDate.ToString("dd/MM/yyyy")</td>
                            <td>
                                <span class="badge badge-@GetStatusBadgeClass(booking.Status)">
                                    @booking.Status.GetDisplayName()
                                </span>
                            </td>
                            <td>@booking.TotalAmount.ToString("N0")₫</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => "warning",
            BookingStatus.Confirmed => "info",
            BookingStatus.CheckedIn => "primary",
            BookingStatus.CheckedOut => "success",
            BookingStatus.Cancelled => "danger",
            BookingStatus.NoShow => "secondary",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Booking Status Chart
        var ctx = document.getElementById('bookingStatusChart').getContext('2d');
        var bookingStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [@Html.Raw(string.Join(",", Model.BookingsByStatus.Keys.Select(k => $"'{k.GetDisplayName()}'")))],
                datasets: [{
                    data: [@string.Join(",", Model.BookingsByStatus.Values)],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
}
