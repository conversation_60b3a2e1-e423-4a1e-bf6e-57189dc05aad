﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - <PERSON>h<PERSON><PERSON> sạn</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HotelBooking.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand fw-bold text-primary" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-building"></i> Khách sạn
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1 me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house"></i> Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#rooms">
                                <i class="bi bi-door-open"></i> Phòng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#services">
                                <i class="bi bi-gear"></i> Dịch vụ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#gallery">
                                <i class="bi bi-images"></i> Hình ảnh
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#contact">
                                <i class="bi bi-telephone"></i> Liên hệ
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            @if (User.IsInRole("Admin"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="Admin" asp-action="Index">
                                        <i class="bi bi-gear"></i> Admin
                                    </a>
                                </li>
                            }
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">Hồ sơ</a></li>
                                    <li><a class="dropdown-item" href="#">Đặt phòng của tôi</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right"></i> Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Account" asp-action="Login">
                                    <i class="bi bi-box-arrow-in-right"></i> Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Account" asp-action="Register">
                                    <i class="bi bi-person-plus"></i> Đăng ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="bi bi-building"></i> Khách sạn</h5>
                    <p class="mb-0">Trải nghiệm dịch vụ khách sạn đẳng cấp với không gian sang trọng và dịch vụ tận tâm.</p>
                </div>
                <div class="col-md-3">
                    <h6>Liên kết nhanh</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">Trang chủ</a></li>
                        <li><a href="#rooms" class="text-light text-decoration-none">Phòng</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Dịch vụ</a></li>
                        <li><a href="#contact" class="text-light text-decoration-none">Liên hệ</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Liên hệ</h6>
                    <p class="mb-1"><i class="bi bi-geo-alt"></i> 123 Đường ABC, Quận 1, TP.HCM</p>
                    <p class="mb-1"><i class="bi bi-telephone"></i> (028) 1234 5678</p>
                    <p class="mb-0"><i class="bi bi-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <p class="mb-0">&copy; 2025 Khách sạn. Tất cả quyền được bảo lưu.</p>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/images.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
