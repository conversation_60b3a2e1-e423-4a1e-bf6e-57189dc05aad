using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HotelBooking.Models
{
    public class Booking
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Mã đặt phòng")]
        [StringLength(20)]
        public string BookingCode { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Người dùng")]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Phòng")]
        public int RoomId { get; set; }

        [Required]
        [Display(Name = "Ngày nhận phòng")]
        [DataType(DataType.Date)]
        public DateTime CheckInDate { get; set; }

        [Required]
        [Display(Name = "Ngày trả phòng")]
        [DataType(DataType.Date)]
        public DateTime CheckOutDate { get; set; }

        [Required]
        [Display(Name = "Số người lớn")]
        [Range(1, 10, ErrorMessage = "Số người lớn phải từ 1 đến 10")]
        public int Adults { get; set; }

        [Display(Name = "Số trẻ em")]
        [Range(0, 10, ErrorMessage = "Số trẻ em phải từ 0 đến 10")]
        public int Children { get; set; } = 0;

        [Required]
        [Display(Name = "Tổng tiền")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [Display(Name = "Trạng thái")]
        public BookingStatus Status { get; set; } = BookingStatus.Pending;

        [Display(Name = "Yêu cầu đặc biệt")]
        [StringLength(1000)]
        public string? SpecialRequests { get; set; }

        [Display(Name = "Ngày đặt")]
        public DateTime BookingDate { get; set; } = DateTime.Now;

        [Display(Name = "Ngày xác nhận")]
        public DateTime? ConfirmationDate { get; set; }

        [Display(Name = "Ngày hủy")]
        public DateTime? CancellationDate { get; set; }

        [Display(Name = "Lý do hủy")]
        [StringLength(500)]
        public string? CancellationReason { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;
        
        [ForeignKey("RoomId")]
        public virtual Room Room { get; set; } = null!;
    }

    public enum BookingStatus
    {
        [Display(Name = "Chờ xác nhận")]
        Pending = 0,
        
        [Display(Name = "Đã xác nhận")]
        Confirmed = 1,
        
        [Display(Name = "Đã nhận phòng")]
        CheckedIn = 2,
        
        [Display(Name = "Đã trả phòng")]
        CheckedOut = 3,
        
        [Display(Name = "Đã hủy")]
        Cancelled = 4,
        
        [Display(Name = "Không đến")]
        NoShow = 5
    }
}
