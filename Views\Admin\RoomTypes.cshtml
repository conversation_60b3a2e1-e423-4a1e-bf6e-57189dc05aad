@model IEnumerable<HotelBooking.Models.RoomType>
@{
    ViewData["Title"] = "Quản lý loại phòng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-house"></i> Quản lý loại phòng
            </h1>
            <a asp-action="CreateRoomType" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Thêm loại phòng mới
            </a>
        </div>
    </div>
</div>

<div class="row">
    @foreach (var roomType in Model)
    {
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">@roomType.Name</h6>
                    <span class="badge badge-@(roomType.IsActive ? "success" : "secondary")">
                        @(roomType.IsActive ? "Hoạt động" : "Không hoạt động")
                    </span>
                </div>
                <div class="room-image @(roomType.Name.ToLower().Contains("standard") ? "standard" : roomType.Name.ToLower().Contains("deluxe") ? "deluxe" : "suite")" style="height: 180px;">
                </div>
                <div class="card-body d-flex flex-column">
                    <p class="card-text text-muted mb-3">@roomType.Description</p>
                    
                    <div class="mb-3">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-right">
                                    <div class="h5 font-weight-bold text-primary">@roomType.PricePerNight.ToString("N0")₫</div>
                                    <div class="small text-muted">Giá/đêm</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="h5 font-weight-bold text-info">@roomType.MaxOccupancy</div>
                                <div class="small text-muted">Người tối đa</div>
                            </div>
                        </div>
                    </div>

                    @if (roomType.Area.HasValue)
                    {
                        <div class="mb-2">
                            <i class="bi bi-rulers text-muted"></i>
                            <span class="text-muted">Diện tích: @roomType.Area m²</span>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(roomType.Amenities))
                    {
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-check-circle text-success"></i>
                                @roomType.Amenities
                            </small>
                        </div>
                    }

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Tạo: @roomType.CreatedAt.ToString("dd/MM/yyyy")
                            </small>
                            <div class="btn-group" role="group">
                                <a asp-action="EditRoomType" asp-route-id="@roomType.Id" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger btn-delete" 
                                        onclick="deleteRoomType(@roomType.Id)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@if (!Model.Any())
{
    <div class="text-center py-5">
        <i class="bi bi-house text-muted" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">Chưa có loại phòng nào</h4>
        <p class="text-muted">Hãy thêm loại phòng đầu tiên cho khách sạn của bạn.</p>
        <a asp-action="CreateRoomType" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Thêm loại phòng mới
        </a>
    </div>
}

@section Scripts {
    <script>
        function deleteRoomType(roomTypeId) {
            if (confirm('Bạn có chắc chắn muốn xóa loại phòng này? Tất cả phòng thuộc loại này cũng sẽ bị ảnh hưởng.')) {
                // Implement delete functionality
                fetch(`/Admin/DeleteRoomType/${roomTypeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra khi xóa loại phòng');
                    }
                });
            }
        }
    </script>
}
