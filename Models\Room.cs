using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HotelBooking.Models
{
    public class Room
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "S<PERSON> phòng")]
        [StringLength(10)]
        public string RoomNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Loại phòng")]
        public int RoomTypeId { get; set; }

        [Display(Name = "Tầng")]
        [Range(1, 50, ErrorMessage = "Tầng phải từ 1 đến 50")]
        public int Floor { get; set; }

        [Display(Name = "Trạng thái")]
        public RoomStatus Status { get; set; } = RoomStatus.Available;

        [Display(Name = "Ghi chú")]
        [StringLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "Cập nhật lần cuối")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("RoomTypeId")]
        public virtual RoomType RoomType { get; set; } = null!;
        
        public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
    }

    public enum RoomStatus
    {
        [Display(Name = "Có sẵn")]
        Available = 0,
        
        [Display(Name = "Đã đặt")]
        Occupied = 1,
        
        [Display(Name = "Bảo trì")]
        Maintenance = 2,
        
        [Display(Name = "Dọn dẹp")]
        Cleaning = 3,
        
        [Display(Name = "Không hoạt động")]
        OutOfOrder = 4
    }
}
