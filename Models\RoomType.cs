using System.ComponentModel.DataAnnotations;

namespace HotelBooking.Models
{
    public class RoomType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Tên loại phòng")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Mô tả")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "Giá mỗi đêm")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal PricePerNight { get; set; }

        [Required]
        [Display(Name = "Sức chứa tối đa")]
        [Range(1, 10, ErrorMessage = "Sức chứa phải từ 1 đến 10 người")]
        public int MaxOccupancy { get; set; }

        [Display(Name = "Diện tích (m²)")]
        [Range(0, 1000, ErrorMessage = "<PERSON>ện tích phải từ 0 đến 1000 m²")]
        public double? Area { get; set; }

        [Display(Name = "Tiện nghi")]
        [StringLength(1000)]
        public string? Amenities { get; set; }

        [Display(Name = "Hình ảnh")]
        [StringLength(200)]
        public string? ImageUrl { get; set; }

        [Display(Name = "Trạng thái hoạt động")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Room> Rooms { get; set; } = new List<Room>();
    }
}
