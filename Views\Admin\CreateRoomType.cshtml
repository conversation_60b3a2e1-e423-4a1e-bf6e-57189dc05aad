@model HotelBooking.Models.RoomType
@{
    ViewData["Title"] = "Thêm loại phòng mới";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i> Thêm loại phòng mới
            </h1>
            <a asp-action="RoomTypes" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Thông tin loại phòng</h6>
            </div>
            <div class="card-body">
                <form asp-action="CreateRoomType" method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Name" class="form-label"></label>
                            <input asp-for="Name" class="form-control" placeholder="Ví dụ: Phòng VIP" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="PricePerNight" class="form-label"></label>
                            <div class="input-group">
                                <input asp-for="PricePerNight" class="form-control" type="number" min="0" step="1000" />
                                <span class="input-group-text">₫</span>
                            </div>
                            <span asp-validation-for="PricePerNight" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="Mô tả về loại phòng này..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="MaxOccupancy" class="form-label"></label>
                            <input asp-for="MaxOccupancy" class="form-control" type="number" min="1" max="10" />
                            <span asp-validation-for="MaxOccupancy" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="Area" class="form-label"></label>
                            <div class="input-group">
                                <input asp-for="Area" class="form-control" type="number" min="0" step="0.1" />
                                <span class="input-group-text">m²</span>
                            </div>
                            <span asp-validation-for="Area" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Amenities" class="form-label"></label>
                        <textarea asp-for="Amenities" class="form-control" rows="2" 
                                  placeholder="Ví dụ: Điều hòa, TV, WiFi, Tủ lạnh, Ban công..."></textarea>
                        <span asp-validation-for="Amenities" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="ImageUrl" class="form-label"></label>
                        <input asp-for="ImageUrl" class="form-control" placeholder="/images/rooms/room-type.jpg" />
                        <span asp-validation-for="ImageUrl" class="text-danger"></span>
                        <div class="form-text">URL hình ảnh đại diện cho loại phòng</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="IsActive" class="form-check-input" checked />
                            <label asp-for="IsActive" class="form-check-label"></label>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="RoomTypes" class="btn btn-secondary me-md-2">Hủy</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Tạo loại phòng
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
