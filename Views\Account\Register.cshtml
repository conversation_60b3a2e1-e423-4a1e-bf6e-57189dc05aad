@model HotelBooking.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-person-plus-fill text-primary" style="font-size: 4rem;"></i>
                        <h2 class="mt-3">Đăng ký tài khoản</h2>
                        <p class="text-muted">Tạo tài khoản mới để đặt phòng</p>
                    </div>

                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FullName" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                    <input asp-for="FullName" class="form-control" placeholder="Họ và tên" />
                                </div>
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                    <input asp-for="Email" class="form-control" placeholder="Email" />
                                </div>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-telephone"></i></span>
                                    <input asp-for="PhoneNumber" class="form-control" placeholder="Số điện thoại" />
                                </div>
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                    <input asp-for="DateOfBirth" class="form-control" />
                                </div>
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                <input asp-for="Address" class="form-control" placeholder="Địa chỉ" />
                            </div>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                    <input asp-for="Password" class="form-control" placeholder="Mật khẩu" />
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Xác nhận mật khẩu" />
                                </div>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-person-plus"></i> Đăng ký
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Đã có tài khoản? 
                            <a asp-action="Login" class="text-decoration-none">Đăng nhập ngay</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
