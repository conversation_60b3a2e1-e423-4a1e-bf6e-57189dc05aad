using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HotelBooking.Data;
using HotelBooking.Models;
using HotelBooking.ViewModels;

namespace HotelBooking.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;

        public AdminController(ApplicationDbContext context, UserManager<ApplicationUser> userManager, SignInManager<ApplicationUser> signInManager)
        {
            _context = context;
            _userManager = userManager;
            _signInManager = signInManager;
        }

        // Dashboard
        public async Task<IActionResult> Index()
        {
            var model = new AdminDashboardViewModel
            {
                TotalRooms = await _context.Rooms.CountAsync(),
                TotalBookings = await _context.Bookings.CountAsync(),
                TotalUsers = await _userManager.Users.CountAsync(),
                TotalRevenue = await _context.Bookings
                    .Where(b => b.Status == BookingStatus.CheckedOut)
                    .SumAsync(b => b.TotalAmount),
                RecentBookings = await _context.Bookings
                    .Include(b => b.User)
                    .Include(b => b.Room)
                    .ThenInclude(r => r.RoomType)
                    .OrderByDescending(b => b.BookingDate)
                    .Take(10)
                    .ToListAsync(),
                BookingsByStatus = await _context.Bookings
                    .GroupBy(b => b.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Status, x => x.Count)
            };

            return View(model);
        }

        // Room Management
        public async Task<IActionResult> Rooms()
        {
            var rooms = await _context.Rooms
                .Include(r => r.RoomType)
                .OrderBy(r => r.RoomNumber)
                .ToListAsync();
            return View(rooms);
        }

        [HttpGet]
        public async Task<IActionResult> CreateRoom()
        {
            ViewBag.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateRoom(Room room)
        {
            if (ModelState.IsValid)
            {
                room.CreatedAt = DateTime.Now;
                room.LastUpdated = DateTime.Now;
                _context.Rooms.Add(room);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Phòng đã được tạo thành công!";
                return RedirectToAction(nameof(Rooms));
            }

            ViewBag.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            return View(room);
        }

        [HttpGet]
        public async Task<IActionResult> EditRoom(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return NotFound();
            }

            ViewBag.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            return View(room);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditRoom(int id, Room room)
        {
            if (id != room.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    room.LastUpdated = DateTime.Now;
                    _context.Update(room);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Phòng đã được cập nhật thành công!";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RoomExists(room.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Rooms));
            }

            ViewBag.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            return View(room);
        }

        // Room Types Management
        public async Task<IActionResult> RoomTypes()
        {
            var roomTypes = await _context.RoomTypes.OrderBy(rt => rt.Name).ToListAsync();
            return View(roomTypes);
        }

        [HttpGet]
        public IActionResult CreateRoomType()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateRoomType(RoomType roomType)
        {
            if (ModelState.IsValid)
            {
                roomType.CreatedAt = DateTime.Now;
                _context.RoomTypes.Add(roomType);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Loại phòng đã được tạo thành công!";
                return RedirectToAction(nameof(RoomTypes));
            }
            return View(roomType);
        }

        // Bookings Management
        public async Task<IActionResult> Bookings()
        {
            var bookings = await _context.Bookings
                .Include(b => b.User)
                .Include(b => b.Room)
                .ThenInclude(r => r.RoomType)
                .OrderByDescending(b => b.BookingDate)
                .ToListAsync();
            return View(bookings);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateBookingStatus(int id, BookingStatus status)
        {
            var booking = await _context.Bookings.FindAsync(id);
            if (booking == null)
            {
                return NotFound();
            }

            booking.Status = status;
            if (status == BookingStatus.Confirmed)
            {
                booking.ConfirmationDate = DateTime.Now;
            }
            else if (status == BookingStatus.Cancelled)
            {
                booking.CancellationDate = DateTime.Now;
            }

            await _context.SaveChangesAsync();
            TempData["Success"] = "Trạng thái đặt phòng đã được cập nhật!";
            return RedirectToAction(nameof(Bookings));
        }

        // Users Management
        public async Task<IActionResult> Users()
        {
            var users = await _userManager.Users.OrderBy(u => u.FullName).ToListAsync();
            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                userViewModels.Add(new UserViewModel
                {
                    User = user,
                    Roles = roles.ToList()
                });
            }

            return View(userViewModels);
        }

        // Reports
        public async Task<IActionResult> Reports()
        {
            var model = new ReportsViewModel
            {
                MonthlyRevenue = await GetMonthlyRevenue(),
                BookingsByMonth = await GetBookingsByMonth(),
                RoomOccupancyRate = await GetRoomOccupancyRate()
            };

            return View(model);
        }

        private bool RoomExists(int id)
        {
            return _context.Rooms.Any(e => e.Id == id);
        }

        private async Task<Dictionary<string, decimal>> GetMonthlyRevenue()
        {
            var result = await _context.Bookings
                .Where(b => b.Status == BookingStatus.CheckedOut && b.BookingDate.Year == DateTime.Now.Year)
                .GroupBy(b => b.BookingDate.Month)
                .Select(g => new { Month = g.Key, Revenue = g.Sum(b => b.TotalAmount) })
                .ToDictionaryAsync(x => $"Tháng {x.Month}", x => x.Revenue);

            return result;
        }

        private async Task<Dictionary<string, int>> GetBookingsByMonth()
        {
            var result = await _context.Bookings
                .Where(b => b.BookingDate.Year == DateTime.Now.Year)
                .GroupBy(b => b.BookingDate.Month)
                .Select(g => new { Month = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => $"Tháng {x.Month}", x => x.Count);

            return result;
        }

        private async Task<double> GetRoomOccupancyRate()
        {
            var totalRooms = await _context.Rooms.CountAsync();
            var occupiedRooms = await _context.Rooms.CountAsync(r => r.Status == RoomStatus.Occupied);
            
            return totalRooms > 0 ? (double)occupiedRooms / totalRooms * 100 : 0;
        }
    }
}
