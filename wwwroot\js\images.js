// Image effects and interactions
document.addEventListener('DOMContentLoaded', function() {
    
    // Add loading animation to images
    const images = document.querySelectorAll('.room-image, .hero-image');
    images.forEach(img => {
        img.style.opacity = '0';
        img.style.transform = 'scale(0.9)';
        
        // Animate in
        setTimeout(() => {
            img.style.transition = 'all 0.6s ease';
            img.style.opacity = '1';
            img.style.transform = 'scale(1)';
        }, Math.random() * 500);
    });
    
    // Add click effect to room images
    const roomImages = document.querySelectorAll('.room-image');
    roomImages.forEach(img => {
        img.addEventListener('click', function() {
            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.width = '20px';
            ripple.style.height = '20px';
            ripple.style.marginLeft = '-10px';
            ripple.style.marginTop = '-10px';
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Parallax effect for hero image
    const heroImage = document.querySelector('.hero-image');
    if (heroImage) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            heroImage.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Service icons animation on scroll
    const serviceIcons = document.querySelectorAll('.service-icon');
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'bounceIn 0.6s ease';
                entry.target.style.animationDelay = Math.random() * 0.3 + 's';
            }
        });
    }, observerOptions);
    
    serviceIcons.forEach(icon => {
        observer.observe(icon);
    });
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes bounceIn {
        0% {
            transform: scale(0.3) translateY(20px);
            opacity: 0;
        }
        50% {
            transform: scale(1.05) translateY(-5px);
        }
        70% {
            transform: scale(0.9) translateY(0px);
        }
        100% {
            transform: scale(1) translateY(0px);
            opacity: 1;
        }
    }
    
    .image-loading {
        position: relative;
        overflow: hidden;
    }
    
    .image-loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: shimmer 1.5s infinite;
    }
    
    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }
`;
document.head.appendChild(style);
