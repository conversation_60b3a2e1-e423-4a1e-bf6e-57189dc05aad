@model IEnumerable<HotelBooking.ViewModels.UserViewModel>
@{
    ViewData["Title"] = "Quản lý người dùng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-people"></i> Quản lý người dùng
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="filterUsers('all')">Tất cả</button>
                <button type="button" class="btn btn-outline-success" onclick="filterUsers('Admin')">Admin</button>
                <button type="button" class="btn btn-outline-info" onclick="filterUsers('Customer')"><PERSON>h<PERSON><PERSON> hàng</button>
                <button type="button" class="btn btn-outline-warning" onclick="filterUsers('inactive')">Không hoạt động</button>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách người dùng</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered datatable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Họ tên</th>
                        <th>Email</th>
                        <th>Số điện thoại</th>
                        <th>Vai trò</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var userViewModel in Model)
                    {
                        var user = userViewModel.User;
                        <tr data-roles="@string.Join(",", userViewModel.Roles)" data-active="@user.IsActive">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        <i class="bi bi-person-circle text-primary" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <strong>@user.FullName</strong>
                                        @if (user.DateOfBirth.HasValue)
                                        {
                                            <br><small class="text-muted">Sinh: @user.DateOfBirth.Value.ToString("dd/MM/yyyy")</small>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td>
                                @user.Email
                                @if (user.EmailConfirmed)
                                {
                                    <i class="bi bi-check-circle text-success" title="Email đã xác thực"></i>
                                }
                                else
                                {
                                    <i class="bi bi-exclamation-circle text-warning" title="Email chưa xác thực"></i>
                                }
                            </td>
                            <td>
                                @if (!string.IsNullOrEmpty(user.PhoneNumber))
                                {
                                    @user.PhoneNumber
                                    @if (user.PhoneNumberConfirmed)
                                    {
                                        <i class="bi bi-check-circle text-success" title="SĐT đã xác thực"></i>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">Chưa có</span>
                                }
                            </td>
                            <td>
                                @foreach (var role in userViewModel.Roles)
                                {
                                    <span class="badge badge-@(role == "Admin" ? "danger" : "primary") me-1">@role</span>
                                }
                            </td>
                            <td>
                                <span class="badge badge-@(user.IsActive ? "success" : "secondary")">
                                    @(user.IsActive ? "Hoạt động" : "Không hoạt động")
                                </span>
                            </td>
                            <td>@user.CreatedAt.ToString("dd/MM/yyyy")</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        Thao tác
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <button class="dropdown-item" onclick="viewUserDetails('@user.Id')">
                                                <i class="bi bi-eye"></i> Xem chi tiết
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" onclick="toggleUserStatus('@user.Id', @user.IsActive.ToString().ToLower())">
                                                <i class="bi bi-@(user.IsActive ? "pause" : "play")-circle"></i> 
                                                @(user.IsActive ? "Vô hiệu hóa" : "Kích hoạt")
                                            </button>
                                        </li>
                                        @if (!userViewModel.Roles.Contains("Admin"))
                                        {
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item" onclick="viewUserBookings('@user.Id')">
                                                    <i class="bi bi-calendar-check"></i> Xem đặt phòng
                                                </button>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <script>
        function filterUsers(filter) {
            const table = $('.datatable').DataTable();
            
            if (filter === 'all') {
                table.search('').draw();
            } else if (filter === 'inactive') {
                // Filter by inactive users
                table.column(4).search('Không hoạt động').draw();
            } else {
                // Filter by role
                table.column(3).search(filter).draw();
            }
        }

        function viewUserDetails(userId) {
            // Load user details via AJAX
            fetch(`/Admin/GetUserDetails/${userId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('userDetailsContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
                })
                .catch(error => {
                    alert('Có lỗi xảy ra khi tải thông tin người dùng');
                });
        }

        function toggleUserStatus(userId, currentStatus) {
            const action = currentStatus ? 'vô hiệu hóa' : 'kích hoạt';
            
            if (confirm(`Bạn có chắc chắn muốn ${action} người dùng này?`)) {
                fetch('/Admin/ToggleUserStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    body: JSON.stringify({ userId: userId, isActive: !currentStatus })
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra khi cập nhật trạng thái người dùng');
                    }
                });
            }
        }

        function viewUserBookings(userId) {
            // Redirect to bookings page with user filter
            window.location.href = `/Admin/Bookings?userId=${userId}`;
        }
    </script>
}
