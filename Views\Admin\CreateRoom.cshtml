@model HotelBooking.Models.Room
@{
    ViewData["Title"] = "Thêm phòng mới";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i> Thêm phòng mới
            </h1>
            <a asp-action="Rooms" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Thông tin phòng</h6>
            </div>
            <div class="card-body">
                <form asp-action="CreateRoom" method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="RoomNumber" class="form-label"></label>
                            <input asp-for="RoomNumber" class="form-control" placeholder="Ví dụ: 101" />
                            <span asp-validation-for="RoomNumber" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="RoomTypeId" class="form-label">Loại phòng</label>
                            <select asp-for="RoomTypeId" class="form-select">
                                <option value="">-- Chọn loại phòng --</option>
                                @foreach (var roomType in ViewBag.RoomTypes as List<RoomType>)
                                {
                                    <option value="@roomType.Id">@roomType.Name - @roomType.PricePerNight.ToString("N0")₫/đêm</option>
                                }
                            </select>
                            <span asp-validation-for="RoomTypeId" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Floor" class="form-label"></label>
                            <input asp-for="Floor" class="form-control" type="number" min="1" max="50" />
                            <span asp-validation-for="Floor" class="text-danger"></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="Status" class="form-label"></label>
                            <select asp-for="Status" class="form-select">
                                <option value="0">Có sẵn</option>
                                <option value="1">Đã đặt</option>
                                <option value="2">Bảo trì</option>
                                <option value="3">Dọn dẹp</option>
                                <option value="4">Không hoạt động</option>
                            </select>
                            <span asp-validation-for="Status" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label"></label>
                        <textarea asp-for="Notes" class="form-control" rows="3" placeholder="Ghi chú về phòng (tùy chọn)"></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Rooms" class="btn btn-secondary me-md-2">Hủy</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Tạo phòng
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
