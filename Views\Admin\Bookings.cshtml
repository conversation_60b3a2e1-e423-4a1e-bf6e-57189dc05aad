@model IEnumerable<HotelBooking.Models.Booking>
@{
    ViewData["Title"] = "Quản lý đặt phòng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-calendar-check"></i> Quản lý đặt phòng
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="filterBookings('all')">Tất cả</button>
                <button type="button" class="btn btn-outline-warning" onclick="filterBookings('Pending')">Chờ xác nhận</button>
                <button type="button" class="btn btn-outline-info" onclick="filterBookings('Confirmed')">Đ<PERSON> xác nhận</button>
                <button type="button" class="btn btn-outline-success" onclick="filterBookings('CheckedOut')"><PERSON><PERSON><PERSON> thành</button>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách đặt phòng</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered datatable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mã đặt phòng</th>
                        <th>Khách hàng</th>
                        <th>Phòng</th>
                        <th>Ngày nhận</th>
                        <th>Ngày trả</th>
                        <th>Khách</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var booking in Model)
                    {
                        <tr data-status="@booking.Status">
                            <td>
                                <strong>@booking.BookingCode</strong>
                                <br>
                                <small class="text-muted">@booking.BookingDate.ToString("dd/MM/yyyy")</small>
                            </td>
                            <td>
                                <div>@booking.User.FullName</div>
                                <small class="text-muted">@booking.User.Email</small>
                                @if (!string.IsNullOrEmpty(booking.User.PhoneNumber))
                                {
                                    <br><small class="text-muted">@booking.User.PhoneNumber</small>
                                }
                            </td>
                            <td>
                                <strong>@booking.Room.RoomNumber</strong>
                                <br>
                                <small class="text-muted">@booking.Room.RoomType.Name</small>
                            </td>
                            <td>@booking.CheckInDate.ToString("dd/MM/yyyy")</td>
                            <td>@booking.CheckOutDate.ToString("dd/MM/yyyy")</td>
                            <td>
                                @booking.Adults người lớn
                                @if (booking.Children > 0)
                                {
                                    <br><small>@booking.Children trẻ em</small>
                                }
                            </td>
                            <td>
                                <strong>@booking.TotalAmount.ToString("N0")₫</strong>
                            </td>
                            <td>
                                <span class="badge badge-@GetStatusBadgeClass(booking.Status)">
                                    @booking.Status.GetDisplayName()
                                </span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        Thao tác
                                    </button>
                                    <ul class="dropdown-menu">
                                        @if (booking.Status == BookingStatus.Pending)
                                        {
                                            <li>
                                                <button class="dropdown-item" onclick="updateBookingStatus(@booking.Id, 1)">
                                                    <i class="bi bi-check-circle text-success"></i> Xác nhận
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" onclick="updateBookingStatus(@booking.Id, 4)">
                                                    <i class="bi bi-x-circle text-danger"></i> Hủy
                                                </button>
                                            </li>
                                        }
                                        @if (booking.Status == BookingStatus.Confirmed)
                                        {
                                            <li>
                                                <button class="dropdown-item" onclick="updateBookingStatus(@booking.Id, 2)">
                                                    <i class="bi bi-door-open text-primary"></i> Nhận phòng
                                                </button>
                                            </li>
                                        }
                                        @if (booking.Status == BookingStatus.CheckedIn)
                                        {
                                            <li>
                                                <button class="dropdown-item" onclick="updateBookingStatus(@booking.Id, 3)">
                                                    <i class="bi bi-door-closed text-info"></i> Trả phòng
                                                </button>
                                            </li>
                                        }
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item" onclick="viewBookingDetails(@booking.Id)">
                                                <i class="bi bi-eye"></i> Xem chi tiết
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => "warning",
            BookingStatus.Confirmed => "info",
            BookingStatus.CheckedIn => "primary",
            BookingStatus.CheckedOut => "success",
            BookingStatus.Cancelled => "danger",
            BookingStatus.NoShow => "secondary",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <script>
        function updateBookingStatus(bookingId, status) {
            const statusNames = {
                1: 'xác nhận',
                2: 'nhận phòng',
                3: 'trả phòng',
                4: 'hủy'
            };
            
            if (confirm(`Bạn có chắc chắn muốn ${statusNames[status]} đặt phòng này?`)) {
                fetch('/Admin/UpdateBookingStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    body: JSON.stringify({ id: bookingId, status: status })
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra khi cập nhật trạng thái');
                    }
                });
            }
        }

        function filterBookings(status) {
            const table = $('.datatable').DataTable();
            if (status === 'all') {
                table.search('').draw();
            } else {
                table.search(status).draw();
            }
        }

        function viewBookingDetails(bookingId) {
            // Implement view details functionality
            alert('Chức năng xem chi tiết sẽ được phát triển');
        }
    </script>
}
