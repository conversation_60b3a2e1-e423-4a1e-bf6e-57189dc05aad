using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using HotelBooking.Models;

namespace HotelBooking.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<RoomType> RoomTypes { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<Booking> Bookings { get; set; }
        public DbSet<News> News { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure Room entity
            builder.Entity<Room>()
                .HasOne(r => r.RoomType)
                .WithMany(rt => rt.Rooms)
                .HasForeignKey(r => r.RoomTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Room>()
                .HasIndex(r => r.<PERSON>)
                .IsUnique();

            // Configure Booking entity
            builder.Entity<Booking>()
                .HasOne(b => b.User)
                .WithMany(u => u.Bookings)
                .HasForeignKey(b => b.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Booking>()
                .HasOne(b => b.Room)
                .WithMany(r => r.Bookings)
                .HasForeignKey(b => b.RoomId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Booking>()
                .HasIndex(b => b.BookingCode)
                .IsUnique();

            // Configure decimal precision
            builder.Entity<RoomType>()
                .Property(rt => rt.PricePerNight)
                .HasColumnType("decimal(18,2)");

            builder.Entity<Booking>()
                .Property(b => b.TotalAmount)
                .HasColumnType("decimal(18,2)");

            // Seed data
            SeedData(builder);
        }

        private void SeedData(ModelBuilder builder)
        {
            // Seed RoomTypes
            builder.Entity<RoomType>().HasData(
                new RoomType
                {
                    Id = 1,
                    Name = "Phòng Standard",
                    Description = "Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản",
                    PricePerNight = 500000,
                    MaxOccupancy = 2,
                    Area = 25,
                    Amenities = "Điều hòa, TV, WiFi, Tủ lạnh mini, Phòng tắm riêng",
                    ImageUrl = "/images/rooms/standard.jpg",
                    IsActive = true,
                    CreatedAt = new DateTime(2024, 1, 1)
                },
                new RoomType
                {
                    Id = 2,
                    Name = "Phòng Deluxe",
                    Description = "Phòng cao cấp với view đẹp và tiện nghi hiện đại",
                    PricePerNight = 800000,
                    MaxOccupancy = 3,
                    Area = 35,
                    Amenities = "Điều hòa, Smart TV, WiFi, Tủ lạnh, Ban công, Phòng tắm cao cấp",
                    ImageUrl = "/images/rooms/deluxe.jpg",
                    IsActive = true,
                    CreatedAt = new DateTime(2024, 1, 1)
                },
                new RoomType
                {
                    Id = 3,
                    Name = "Phòng Suite",
                    Description = "Phòng suite sang trọng với không gian rộng rãi",
                    PricePerNight = 1200000,
                    MaxOccupancy = 4,
                    Area = 50,
                    Amenities = "Điều hòa, Smart TV, WiFi, Tủ lạnh, Phòng khách riêng, Jacuzzi, Ban công lớn",
                    ImageUrl = "/images/rooms/suite.jpg",
                    IsActive = true,
                    CreatedAt = new DateTime(2024, 1, 1)
                }
            );
        }
    }
}
