@model HotelBooking.ViewModels.ReportsViewModel
@{
    ViewData["Title"] = "Báo cáo";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-graph-up"></i> Báo cáo
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Doanh thu tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            @Model.MonthlyRevenue.Values.LastOrDefault().ToString("N0")₫
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Đặt phòng tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            @Model.BookingsByMonth.Values.LastOrDefault()
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Tỷ lệ lấp đầy
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">@Model.RoomOccupancyRate.ToString("F1")%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: @Model.RoomOccupancyRate%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-pie-chart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Doanh thu theo tháng (@DateTime.Now.Year)</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <div class="dropdown-header">Tùy chọn:</div>
                        <a class="dropdown-item" href="#" onclick="exportChart('revenue')">Xuất biểu đồ</a>
                        <a class="dropdown-item" href="#" onclick="printChart('revenue')">In biểu đồ</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Đặt phòng theo tháng</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="bookingsChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    @foreach (var booking in Model.BookingsByMonth.Take(3))
                    {
                        <span class="mr-2">
                            <i class="bi bi-circle-fill text-primary"></i> @booking.Key: @booking.Value
                        </span>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Tables -->
<div class="row">
    <!-- Monthly Revenue Table -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Chi tiết doanh thu</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Tháng</th>
                                <th>Doanh thu</th>
                                <th>So với tháng trước</th>
                            </tr>
                        </thead>
                        <tbody>
                            @{
                                decimal previousRevenue = 0;
                            }
                            @foreach (var revenue in Model.MonthlyRevenue)
                            {
                                var change = previousRevenue > 0 ? ((revenue.Value - previousRevenue) / previousRevenue * 100) : 0;
                                <tr>
                                    <td>@revenue.Key</td>
                                    <td>@revenue.Value.ToString("N0")₫</td>
                                    <td>
                                        @if (change > 0)
                                        {
                                            <span class="text-success">
                                                <i class="bi bi-arrow-up"></i> +@change.ToString("F1")%
                                            </span>
                                        }
                                        else if (change < 0)
                                        {
                                            <span class="text-danger">
                                                <i class="bi bi-arrow-down"></i> @change.ToString("F1")%
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                                previousRevenue = revenue.Value;
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Bookings Table -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Chi tiết đặt phòng</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Tháng</th>
                                <th>Số đặt phòng</th>
                                <th>So với tháng trước</th>
                            </tr>
                        </thead>
                        <tbody>
                            @{
                                int previousBookings = 0;
                            }
                            @foreach (var booking in Model.BookingsByMonth)
                            {
                                var change = previousBookings > 0 ? ((double)(booking.Value - previousBookings) / previousBookings * 100) : 0;
                                <tr>
                                    <td>@booking.Key</td>
                                    <td>@booking.Value</td>
                                    <td>
                                        @if (change > 0)
                                        {
                                            <span class="text-success">
                                                <i class="bi bi-arrow-up"></i> +@change.ToString("F1")%
                                            </span>
                                        }
                                        else if (change < 0)
                                        {
                                            <span class="text-danger">
                                                <i class="bi bi-arrow-down"></i> @change.ToString("F1")%
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                                previousBookings = booking.Value;
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        var revenueCtx = document.getElementById('revenueChart').getContext('2d');
        var revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [@Html.Raw(string.Join(",", Model.MonthlyRevenue.Keys.Select(k => $"'{k}'")))],
                datasets: [{
                    label: 'Doanh thu (₫)',
                    data: [@string.Join(",", Model.MonthlyRevenue.Values)],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('vi-VN').format(value) + '₫';
                            }
                        }
                    }
                }
            }
        });

        // Bookings Chart
        var bookingsCtx = document.getElementById('bookingsChart').getContext('2d');
        var bookingsChart = new Chart(bookingsCtx, {
            type: 'doughnut',
            data: {
                labels: [@Html.Raw(string.Join(",", Model.BookingsByMonth.Keys.Select(k => $"'{k}'")))],
                datasets: [{
                    data: [@string.Join(",", Model.BookingsByMonth.Values)],
                    backgroundColor: [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796',
                        '#5a5c69', '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6610f2'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function exportChart(chartType) {
            alert('Chức năng xuất biểu đồ sẽ được phát triển');
        }

        function printChart(chartType) {
            window.print();
        }
    </script>
}
