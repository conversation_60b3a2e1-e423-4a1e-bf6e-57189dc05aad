using HotelBooking.Models;

namespace HotelBooking.ViewModels
{
    public class AdminDashboardViewModel
    {
        public int TotalRooms { get; set; }
        public int TotalBookings { get; set; }
        public int TotalUsers { get; set; }
        public decimal TotalRevenue { get; set; }
        public List<Booking> RecentBookings { get; set; } = new List<Booking>();
        public Dictionary<BookingStatus, int> BookingsByStatus { get; set; } = new Dictionary<BookingStatus, int>();
    }

    public class UserViewModel
    {
        public ApplicationUser User { get; set; } = null!;
        public List<string> Roles { get; set; } = new List<string>();
    }

    public class ReportsViewModel
    {
        public Dictionary<string, decimal> MonthlyRevenue { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, int> BookingsByMonth { get; set; } = new Dictionary<string, int>();
        public double RoomOccupancyRate { get; set; }
    }
}
