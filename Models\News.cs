using System.ComponentModel.DataAnnotations;

namespace HotelBooking.Models
{
    public class News
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Tiêu đề")]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "Tóm tắt")]
        [StringLength(500)]
        public string? Summary { get; set; }

        [Required]
        [Display(Name = "Nội dung")]
        public string Content { get; set; } = string.Empty;

        [Display(Name = "Hình ảnh đại diện")]
        [StringLength(200)]
        public string? ImageUrl { get; set; }

        [Display(Name = "Tác giả")]
        [StringLength(100)]
        public string? Author { get; set; }

        [Display(Name = "Danh mục")]
        public NewsCategory Category { get; set; } = NewsCategory.General;

        [Display(Name = "Trạng thái")]
        public NewsStatus Status { get; set; } = NewsStatus.Draft;

        [Display(Name = "Nổi bật")]
        public bool IsFeatured { get; set; } = false;

        [Display(Name = "Lượt xem")]
        public int ViewCount { get; set; } = 0;

        [Display(Name = "Ngày tạo")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "Ngày xuất bản")]
        public DateTime? PublishedAt { get; set; }

        [Display(Name = "Cập nhật lần cuối")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "Tags")]
        [StringLength(500)]
        public string? Tags { get; set; }

        [Display(Name = "Meta Description")]
        [StringLength(160)]
        public string? MetaDescription { get; set; }
    }

    public enum NewsCategory
    {
        [Display(Name = "Tin tức chung")]
        General = 0,
        
        [Display(Name = "Khuyến mãi")]
        Promotion = 1,
        
        [Display(Name = "Sự kiện")]
        Event = 2,
        
        [Display(Name = "Thông báo")]
        Announcement = 3,
        
        [Display(Name = "Dịch vụ")]
        Service = 4
    }

    public enum NewsStatus
    {
        [Display(Name = "Bản nháp")]
        Draft = 0,
        
        [Display(Name = "Đã xuất bản")]
        Published = 1,
        
        [Display(Name = "Đã ẩn")]
        Hidden = 2,
        
        [Display(Name = "Đã xóa")]
        Deleted = 3
    }
}
